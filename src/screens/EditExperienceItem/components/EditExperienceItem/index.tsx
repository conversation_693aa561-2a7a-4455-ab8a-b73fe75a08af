import type React from 'react';
import { useState, useRef } from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View, Animated } from 'react-native';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Checkbox from '@/src/components/Checkbox';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import TextView from '@/src/components/TextView';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { showToast } from '@/src/utilities/toast';
import AddItem from '@/src/assets/svgs/AddItem';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import { toMonthYear } from '../utils';
import type {
  EditExperienceItemPropsI,
  FieldTypeI,
  ShipItemProps,
  UseEditExperienceItemReturn,
} from './types';
import useEditExperienceItem from './useHook';

const ShipItem: React.FC<ShipItemProps> = ({
  field,
  onAddEdit,
  deleteShip,
  isDeleting,
  company,
  index,
}) => {
  const [shipDeleteId, setShipDeleteId] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const addButtonScale = useRef(new Animated.Value(1)).current;
  const editButtonScale = useRef(new Animated.Value(1)).current;
  const deleteButtonScale = useRef(new Animated.Value(1)).current;

  const designationSelection = useSelector(selectSelectionByKey(`designation-${index}`));

  const canAddShips =
    company?.id && (designationSelection?.id || field?.designation?.id) && field?.fromDate;

  const animateButton = (scale: Animated.Value) => {
    Animated.sequence([
      Animated.timing(scale, {
        toValue: 1.1,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  return (
    <>
      <View className="px-4 py-2 bg-[#F5F5F5] rounded-lg">
        <View className="flex-row items-center justify-between">
          <TextView subtitle="Ship details" subtitleClassName="font-bold text-black" />
          {canAddShips && (
            <Animated.View style={{ transform: [{ scale: addButtonScale }] }}>
              <Pressable
                onPress={() => {
                  animateButton(addButtonScale);
                  onAddEdit(field);
                }}
              >
                <AddItem />
              </Pressable>
            </Animated.View>
          )}
        </View>
        {(field.ships ?? []).length === 0 && (
          <NotFound
            imageStyle={{ width: '50%', height: 100 }}
            fullScreen={false}
            className="py-5"
          />
        )}
        {(field.ships ?? []).map((ship: any) => {
          return (
            <View className="py-3" key={ship.id}>
              <TextView subtitle={`${ship.name}`} subtitleClassName="font-bold" />
              <View className="flex-row items-center justify-between">
                <TextView
                  subtitle={`${toMonthYear(ship.fromDate)} - ${
                    ship.isPresent || !ship.toDate ? 'Present' : toMonthYear(ship.toDate)
                  }`}
                />
                <View className="flex-row items-center gap-4">
                  <Animated.View style={{ transform: [{ scale: editButtonScale }] }}>
                    <Pressable
                      onPress={() => {
                        animateButton(editButtonScale);
                        onAddEdit(field, ship.id);
                      }}
                    >
                      <EditPencil width={2.3} height={2.3} />
                    </Pressable>
                  </Animated.View>
                  <Animated.View style={{ transform: [{ scale: deleteButtonScale }] }}>
                    <Pressable
                      onPress={() => {
                        animateButton(deleteButtonScale);
                        setShipDeleteId(ship.id);
                        setIsVisible(true);
                      }}
                    >
                      <DeleteIcon width={2.3} height={2.3} />
                    </Pressable>
                  </Animated.View>
                </View>
              </View>
            </View>
          );
        })}
      </View>
      <CustomModal
        isVisible={isVisible}
        onCancel={() => {
          setShipDeleteId(null);
          setIsVisible(false);
        }}
        title="Are you sure you want to delete this ship?"
        confirmText="Delete"
        confirmButtonVariant="danger"
        onConfirm={() => {
          if (shipDeleteId) {
            deleteShip(field, shipDeleteId);
            setIsVisible(false);
          }
        }}
        isConfirming={isDeleting}
      />
    </>
  );
};

const EditExperienceItem: React.FC<EditExperienceItemPropsI> = ({
  onBack,
  profileId,
  experienceId,
}) => {
  const [removingIndex, setRemovingIndex] = useState<number | null>(null);
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
  const scrollViewRef = useRef<ScrollView>(null);
  const addDesignationScale = useRef(new Animated.Value(1)).current;
  const removeButtonScales = useRef<{ [key: number]: Animated.Value }>({}).current;

  const {
    isSubmitting,
    loading,
    isAddingDesignation,
    handleAddDesignation,
    handleAddEditShip,
    handleDeleteShip,
    isDeleting,
    localDesignations,
    setLocalDesignations,
    getDateChangeHandler,
    localEntity,
    handleSubmit,
    getFieldError,
    handlePresentCheckbox,
    designationSelections,
    hasChanges,
  }: UseEditExperienceItemReturn = useEditExperienceItem(profileId, experienceId);

  const entitySelection = useSelector(selectSelectionByKey('entity'));

  const animateButton = (scale: Animated.Value) => {
    Animated.sequence([
      Animated.timing(scale, {
        toValue: 1.1,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const canAddDesignation = () => {
    if (localDesignations.length === 0) return true;

    const lastDesignation = localDesignations[localDesignations.length - 1];
    const lastDesignationSelection = designationSelections[localDesignations.length - 1];

    return (
      (lastDesignationSelection?.id || lastDesignation?.designation?.id) &&
      lastDesignation?.fromDate &&
      (lastDesignation?.isPresent || lastDesignation?.toDate)
    );
  };

  const handleAddDesignationClick = () => {
    if (canAddDesignation()) {
      animateButton(addDesignationScale);
      handleAddDesignation();
    } else {
      showToast({
        message: 'Incomplete Designation',
        description:
          'Please complete the current designation details (Designation, From, and To) before adding a new one.',
        type: 'error',
      });
    }
  };

  const handleRemoveDesignation = async (index: number) => {
    if (!removeButtonScales[index]) {
      removeButtonScales[index] = new Animated.Value(1);
    }

    animateButton(removeButtonScales[index]);
    setRemovingIndex(index);

    setLocalDesignations((prev: any[]) => prev.filter((_: any, i: number) => i !== index));

    setTimeout(() => {
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({ y: 0, animated: true });
      }
    }, 100);

    showToast({
      message: 'Designation Removed',
      description:
        "The designation has been removed. Tapping 'Save' will permanently delete it, or go back to restore it.",
      type: 'success',
    });

    setRemovingIndex(null);
  };

  const sortDesignations = (designations: any[]) => {
    return [...designations].sort((a, b) => {
      if (!a.fromDate && !b.fromDate) return 0;
      if (!a.fromDate) return 1;
      if (!b.fromDate) return -1;
      return new Date(b.fromDate).getTime() - new Date(a.fromDate).getTime();
    });
  };

  const sortedDesignations = sortDesignations(localDesignations);

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <ScrollView
      ref={scrollViewRef}
      className="flex-1 bg-white"
      showsVerticalScrollIndicator={false}
    >
      <View className="px-4">
        <View className="flex-row items-center justify-between py-4">
          <BackButton onBack={onBack} label="Edit Experience" />
          <Pressable
            onPress={() => {
              setIsSubmitted(true);
              handleSubmit();
            }}
            disabled={isSubmitting || !hasChanges}
          >
            <Text
              className={`text-lg font-medium ${
                isSubmitting || !hasChanges ? 'text-gray-400' : 'text-[#448600]'
              }`}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>

        <EntitySearch
          title="Company"
          placeholder="Enter Company ..."
          selectionKey="entity/organisation"
          data={entitySelection ? entitySelection.name : localEntity?.name || ''}
          error={
            isSubmitted && !entitySelection && !localEntity?.id ? 'Company is required' : undefined
          }
        />

        <Pressable onPress={handleAddDesignationClick} disabled={isAddingDesignation}>
          <View className="flex-row justify-between mt-4 items-center">
            <TextView subtitle="Designation Details" subtitleClassName="font-bold text-black" />
            <View className="flex-row items-center gap-2">
              {isAddingDesignation && <ActivityIndicator size="small" color="#448600" />}
              <Animated.View style={{ transform: [{ scale: addDesignationScale }] }}>
                <AddItem color={canAddDesignation() ? '#448600' : '#9CA3AF'} />
              </Animated.View>
            </View>
          </View>
        </Pressable>

        {sortedDesignations.map((item: any, originalIndex: number) => {
          const actualIndex = localDesignations.findIndex((d) => d === item);
          const fromDateError = getFieldError('fromDate', actualIndex);
          const toDateError = getFieldError('toDate', actualIndex);
          const designationError = getFieldError('designation', actualIndex);
          const isRemoving = removingIndex === actualIndex;
          const designationSelection = designationSelections[actualIndex];

          if (!removeButtonScales[actualIndex]) {
            removeButtonScales[actualIndex] = new Animated.Value(1);
          }

          return (
            <View className="" key={`designation-${actualIndex}-${item.id || 'new'}`}>
              <EntitySearch
                title={`Designation ${originalIndex + 1}`}
                placeholder="Enter Designation"
                selectionKey={`designation-${actualIndex}`}
                data={designationSelection?.name || item?.designation?.name || ''}
                error={
                  isSubmitted && !designationSelection && !item?.designation?.id
                    ? 'Designation is required'
                    : designationError
                }
              />

              <View className="flex-row mb-6 mt-4">
                <View className="flex-1 mr-2">
                  <DatePicker
                    title="From"
                    selectedDate={item.fromDate || ''}
                    onDateChange={getDateChangeHandler(actualIndex, 'fromDate')}
                    showMonthYear={true}
                    className={fromDateError ? 'border-red-500' : ''}
                  />
                  {fromDateError && (
                    <Text className="text-red-500 text-xs mt-1">{fromDateError}</Text>
                  )}
                </View>
                <View className="flex-1 mr-2">
                  <DatePicker
                    title="To"
                    selectedDate={item.isPresent ? '' : item.toDate || ''}
                    onDateChange={getDateChangeHandler(actualIndex, 'toDate')}
                    showMonthYear={true}
                    disabled={item.isPresent}
                    className={toDateError ? 'border-red-500' : ''}
                  />
                  {toDateError && <Text className="text-red-500 text-xs mt-1">{toDateError}</Text>}
                  <Checkbox
                    label="Present"
                    className="pt-3"
                    labelClassName="text-base text-sm"
                    onValueChange={(checked) => handlePresentCheckbox(actualIndex, checked)}
                    checked={item.isPresent || false}
                  />
                </View>
              </View>

              <ShipItem
                field={
                  {
                    ...item,
                    designation: designationSelection || item.designation,
                  } as FieldTypeI
                }
                onAddEdit={(field: FieldTypeI, shipId?: string) => handleAddEditShip(field, shipId)}
                deleteShip={(field: FieldTypeI, shipId: string) => handleDeleteShip(field, shipId)}
                isDeleting={isDeleting}
                company={localEntity as SearchResultI}
                index={actualIndex}
              />

              <Animated.View style={{ transform: [{ scale: removeButtonScales[actualIndex] }] }}>
                <Pressable
                  onPress={() => handleRemoveDesignation(actualIndex)}
                  disabled={isRemoving}
                  className={`flex-row items-center self-end mb-4 gap-2 mt-2 ${
                    isRemoving ? 'opacity-50' : ''
                  }`}
                >
                  <DeleteIcon color="red" width={1.75} height={1.75} />
                  <Text className="text-red-500">{isRemoving ? 'Removing...' : 'Remove'}</Text>
                </Pressable>
              </Animated.View>
            </View>
          );
        })}
      </View>
    </ScrollView>
  );
};

export default EditExperienceItem;
