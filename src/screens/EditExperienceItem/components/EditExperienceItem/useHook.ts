import type React from 'react';
import { useEffect, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectAllDesignations } from '@/src/redux/selectors/experience';
import { selectAllSelections, selectSelectionByKey } from '@/src/redux/selectors/search';
import { clearSelectionAsync } from '@/src/redux/slices/entitysearch/searchSlice';
import type { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import {
  addExperienceAsync,
  addShipExperience,
} from '@/src/redux/slices/experience/experienceSlice';
import { type AppDispatch, store } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { navigate } from '@/src/utilities/navigation';
import { showToast } from '@/src/utilities/toast';
import { apiCall } from '@/src/services/api';
import { generateExperiencePayload } from '../utils';
import { editExperienceSchema } from './schema';
import type {
  ApiResponseTypeI,
  DesignationsI,
  DesignationWithDateI,
  FieldTypeI,
  UseEditExperienceItemReturn,
  ExperienceFormDataI,
} from './types';

const useEditExperienceItem = (
  profileId: string,
  experienceId?: string,
): UseEditExperienceItemReturn => {
  const [loading, setLoading] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isAddingDesignation, setIsAddingDesignation] = useState<boolean>(false);
  const dispatch = useDispatch<AppDispatch>();
  const entity: SearchResultI = useSelector(selectSelectionByKey('entity'));
  const designations: DesignationsI[] = useSelector(selectAllDesignations);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [localEntity, setLocalEntity] = useState<SearchResultI | undefined>();
  const [initialEntity, setInitialEntity] = useState<SearchResultI | undefined>();
  const [initialDesignations, setInitialDesignations] = useState<DesignationWithDateI[]>([]);
  const [error, setError] = useState<Error | null>(null);
  const [designationSelections, setDesignationSelections] = useState<(SearchResultI | undefined)[]>(
    [],
  );

  const navigation = useNavigation();

  const form = useForm<ExperienceFormDataI>({
    resolver: zodResolver(editExperienceSchema),
    mode: 'onChange',
    defaultValues: {
      designations: [],
    },
  });

  const { append } = useFieldArray({
    control: form.control,
    name: 'designations',
  });

  const watchedDesignations = form.watch('designations');

  const entityChanged =
    localEntity?.id !== initialEntity?.id ||
    localEntity?.name !== initialEntity?.name ||
    localEntity?.dataType !== initialEntity?.dataType;

  const designationsChanged =
    watchedDesignations.length !== initialDesignations.length ||
    watchedDesignations.some((designation, index) => {
      const initial = initialDesignations[index];
      if (!initial) return true;
      return (
        designation.designation?.id !== initial.designation?.id ||
        designation.fromDate !== initial.fromDate ||
        designation.toDate !== initial.toDate ||
        designation.isPresent !== initial.isPresent ||
        (designation.ships?.length || 0) !== (initial.ships?.length || 0) ||
        designation.ships?.some((ship, shipIndex) => {
          const initialShip = initial.ships?.[shipIndex];
          return !initialShip || ship.id !== initialShip.id;
        })
      );
    });

  const hasChanges = form.formState.isDirty || entityChanged || designationsChanged;

  const triggerErrorBoundary = (error: Error): void => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  useEffect(() => {
    if (watchedDesignations) {
      const selections: (SearchResultI | undefined)[] = [];
      for (let i = 0; i < watchedDesignations.length; i++) {
        const selection = store.getState().search.selections[`designation-${i}`];
        selections.push(selection);
      }
      setDesignationSelections(selections);
    }
  }, [watchedDesignations]);

  const getFieldError = (fieldName: string, index: number): string | undefined => {
    const fieldErrors = form.formState.errors.designations?.[index];
    if (fieldErrors && typeof fieldErrors === 'object' && fieldName in fieldErrors) {
      const error = fieldErrors[fieldName as keyof typeof fieldErrors];
      if (error && typeof error === 'object' && 'message' in error) {
        return String(error.message);
      }
      return String(error);
    }
    return undefined;
  };

  const handleAddDesignation = (): void => {
    setIsAddingDesignation(true);
    const newDesignation: any = {
      id: undefined,
      designation: undefined,
      name: undefined,
      experienceDesignationId: undefined,
      fromDate: '',
      toDate: undefined,
      isPresent: false,
      ships: [],
    };
    append(newDesignation);
    setTimeout(() => {
      setIsAddingDesignation(false);
    }, 300);
  };

  const handleDesignationChange = (index: number, selectedDesignation: SearchResultI): void => {
    form.setValue(`designations.${index}.designation`, selectedDesignation);
    form.trigger(`designations.${index}`);
  };

  const handlePresentCheckbox = (index: number, isPresent: boolean): void => {
    form.setValue(`designations.${index}.isPresent`, isPresent);
    if (isPresent) {
      form.setValue(`designations.${index}.toDate`, undefined);
      form.clearErrors(`designations.${index}.toDate`);
    }
    form.trigger(`designations.${index}`);
  };

  const getDateChangeHandler = (index: number, field: 'fromDate' | 'toDate') => {
    return (dateValue: React.SetStateAction<Date>): void => {
      let date: Date;
      if (typeof dateValue === 'function') {
        const currentValue = form.getValues(`designations.${index}.${field}`);
        const currentDate = currentValue ? new Date(currentValue) : new Date();
        date = dateValue(currentDate);
      } else {
        date = dateValue;
      }
      if (date instanceof Date && !isNaN(date.getTime())) {
        const dateString: string = date.toISOString().split('T')[0];
        form.setValue(`designations.${index}.${field}`, dateString);
        form.clearErrors(`designations.${index}.${field}`);
        form.trigger(`designations.${index}`);
      }
    };
  };

  const fetchExperience = async (experienceId: string): Promise<void> => {
    try {
      setLoading(true);
      const response: ApiResponseTypeI = await apiCall(
        `/backend/api/v1/career/profile-experience/${experienceId}`,
        'GET',
        { isAuth: true },
      );
      const processedDesignations = response.designations.map((designation: any) => ({
        ...designation,
        fromDate: designation.fromDate || '',
        toDate: designation.toDate || undefined,
        isPresent: !designation.toDate || designation.toDate === null,
        ships: designation.ships || [],
      }));
      setLocalEntity(response.entity);
      setInitialEntity(response.entity);
      setInitialDesignations(processedDesignations);
      form.reset({ designations: processedDesignations });
    } catch (error: any) {
      triggerErrorBoundary(
        new Error(
          'Failed to load experience details: ' +
            (error instanceof Error ? error.message : 'Unknown error'),
        ),
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (experienceId) {
      fetchExperience(experienceId);
    }
  }, [experienceId]);

  const refetch = (): void => {
    if (experienceId) {
      fetchExperience(experienceId);
    }
  };

  useEffect(() => {
    setLocalEntity(entity);
  }, [entity]);

  useEffect(() => {
    designations.forEach((designation: DesignationsI) => {
      const index = Number(designation.indexKey);
      const currentDesignations = form.getValues('designations');
      if (currentDesignations[index] && !currentDesignations[index].designation) {
        handleDesignationChange(index, {
          id: designation.id,
          name: designation.name,
          dataType: designation.dataType,
        });
      }
    });
  }, [designations]);

  const clearFields = async (): Promise<void> => {
    await dispatch(clearSelectionAsync('entity'));
    const state = store.getState();
    const selections: any = selectAllSelections(state);

    Object.keys(selections).forEach(async (key: string) => {
      if (key.startsWith('designation') || key.startsWith('skill')) {
        await dispatch(clearSelectionAsync(key));
      }
    });
  };

  const clearSkillsSelections = async (): Promise<void> => {
    const state = store.getState();
    const selections: any = selectAllSelections(state);
    Object.keys(selections).forEach(async (key: string) => {
      if (key.startsWith('skill')) {
        await dispatch(clearSelectionAsync(key));
      }
    });
  };

  const handleSubmit = async (): Promise<void> => {
    const isValid = await form.trigger();
    if (!isValid) {
      return showToast({
        message: 'Validation Error',
        description: 'Please fix the errors in the form',
        type: 'error',
      });
    }

    const formData = form.getValues();
    if (formData.designations.length <= 0) {
      return showToast({
        message: 'Validation',
        description: 'At least one designation is needed',
        type: 'error',
      });
    }

    setIsSubmitting(true);
    try {
      const payload: any = generateExperiencePayload(
        localEntity!,
        initialEntity!,
        formData.designations,
        initialDesignations,
        experienceId,
      );
      await dispatch(addExperienceAsync({ payload })).unwrap();
      showToast({
        message: 'Success',
        description: 'Experience updated successfully',
        type: 'success',
      });

      await clearFields();
      navigation.goBack();
    } catch (error: any) {
      showToast({
        message: 'Error',
        description: 'Failed to update experience',
        type: 'error',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddEditShip = (field: FieldTypeI, shipId?: string): void => {
    const formData = form.getValues();
    const data: any = generateExperiencePayload(
      localEntity!,
      initialEntity!,
      formData.designations,
      initialDesignations,
      experienceId,
      field,
    );
    const entityId: string | undefined = localEntity?.id;

    navigate('EditShipItem', {
      data,
      field,
      shipId,
      entityId,
      refetch: async () => {
        await clearSkillsSelections();
        refetch();
      },
    });
  };

  const handleDeleteShip = async (field: FieldTypeI, shipId: string): Promise<void> => {
    try {
      setIsDeleting(true);
      const payload: any = [
        {
          id: experienceId,
          opr: 'NESTED_OPR' as 'UPDATE' | 'DELETE' | 'CREATE' | 'NESTED_OPR',
          designations: [
            {
              id: field.id,
              opr: 'NESTED_OPR',
              ships: [
                {
                  id: shipId,
                  opr: 'DELETE' as 'UPDATE' | 'DELETE' | 'CREATE' | 'NESTED_OPR',
                },
              ],
            },
          ],
        },
      ];

      await dispatch(addShipExperience({ payload })).unwrap();

      const currentDesignations = form.getValues('designations');
      const updatedDesignations = currentDesignations.map((designation: any) => {
        if (designation.id === field.id) {
          const updatedShips: any[] =
            designation.ships?.filter((ship: any) => ship.id !== shipId) || [];
          return {
            ...designation,
            ships: updatedShips,
          };
        }
        return designation;
      });

      form.setValue('designations', updatedDesignations);

      await clearSkillsSelections();
    } catch (error: any) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to delete ship',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later',
            type: 'error',
          });
        },
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const setLocalDesignations = async (
    updater: React.SetStateAction<DesignationWithDateI[]>,
  ): Promise<void> => {
    const currentDesignations = form.getValues('designations');
    const newDesignations =
      typeof updater === 'function' ? updater(currentDesignations as any) : updater;
    const removedIndexes: number[] = [];

    currentDesignations.forEach((_, index) => {
      if (!newDesignations.find((_, newIndex) => newIndex === index)) {
        removedIndexes.push(index);
      }
    });

    for (const index of removedIndexes) {
      await dispatch(clearSelectionAsync(`designation-${index}`));
    }

    form.setValue('designations', newDesignations as any);
    form.trigger('designations');
  };

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      await clearSkillsSelections();
    });

    return unsubscribe;
  }, [navigation]);

  const mappedDesignations: DesignationWithDateI[] = watchedDesignations
    ? watchedDesignations.map((item: any) => ({
        ...item,
        ships: item.ships || [],
        designation: item.designation || undefined,
        fromDate: item.fromDate || '',
        toDate: item.toDate || undefined,
      }))
    : [];

  return {
    designations,
    isSubmitting,
    loading,
    isAddingDesignation,
    handleSubmit,
    handleAddDesignation,
    handleAddEditShip,
    handleDeleteShip,
    isDeleting,
    localDesignations: mappedDesignations,
    getDateChangeHandler,
    localEntity,
    setLocalDesignations,
    getFieldError,
    handlePresentCheckbox,
    designationSelections,
    hasChanges,
  };
};

export default useEditExperienceItem;
