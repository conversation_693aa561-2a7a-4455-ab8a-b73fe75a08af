import React from 'react'
import { Animated, Pressable, Text, View } from 'react-native'
import ReplyPreview from '../ReplyPreview'
import Attachment from '@/src/assets/svgs/Attachment'
import { ActivityIndicator } from 'react-native'
import Send from '@/src/assets/svgs/Send'
import Microphone from '@/src/assets/svgs/Microphone'

const ChatInput = () => {
    return (
        <>
            {!isSelectionMode && (
                <Animated.View
                    className="border-t border-gray-200 bg-white absolute left-0 right-0"
                    style={{
                        paddingVertical: 12,
                        bottom: 0,
                        transform: [{ translateY: inputTranslateY }],
                    }}
                >
                    {replyPreview && <ReplyPreview message={replyPreview} onClose={handleCloseReply} />}

                    {mediaPreview.length > 0 && (
                        <View className="px-4 pb-3">
                            {renderMediaPreview()}
                            <Text className="text-sm text-gray-500 mb-2">Add a caption (optional)</Text>
                        </View>
                    )}

                    {isRecording ? (
                        <View className="px-4 mb-3">
                            <View className="flex-row items-center justify-between bg-gray-100 p-3 rounded-lg">
                                <View className="flex-row items-center gap-2">
                                    <View className="h-3 w-3 rounded-full bg-gray-500" />
                                    <Text className="text-gray-700 font-medium">Recording {recordingTime}</Text>
                                </View>
                                <Pressable
                                    className="bg-gray-500 rounded-full px-3 py-1"
                                    onPress={handleMicrophonePress}
                                >
                                    <Text className="text-white font-bold text-xs">STOP</Text>
                                </Pressable>
                            </View>
                        </View>
                    ) : recordedAudioFile ? (
                        renderAudioPreview()
                    ) : null}

                    <View className="flex-row items-end px-4 gap-3">
                        <View className="flex-1 relative border rounded-full bg-[#F3ECEC] border-[#DEDEDE] pl-4 pr-14">
                            <TextInput
                                ref={textInputRef}
                                value={messageText}
                                onChangeText={handleTextInputChange}
                                placeholder={
                                    mediaPreview.length > 0 || recordedAudioFile
                                        ? 'Add a caption...'
                                        : replyPreview
                                            ? replyPreview.isEditing
                                                ? 'Edit message...'
                                                : 'Reply to message...'
                                            : 'Type a message'
                                }
                                placeholderTextColor="#9CA3AF"
                                className="text-sm text-gray-900 py-3"
                                textAlignVertical="center"
                                editable={!isTextInputDisabled}
                                multiline={false}
                                blurOnSubmit={false}
                            />
                            <Pressable
                                onPress={handleSendMessage}
                                disabled={isSendButtonDisabled}
                                className={`absolute right-0 bottom-0 w-11 h-11 rounded-full items-center justify-center ${isSendButtonDisabled ? 'bg-gray-400/50' : 'bg-green-800'
                                    }`}
                            >
                                {isAudioUploading ? (
                                    <ActivityIndicator size="small" color="#ffffff" />
                                ) : (
                                    <Send color="#ffffff" width={2} height={2} />
                                )}
                            </Pressable>
                        </View>
                        <Pressable
                            className="w-11 h-11 rounded-full items-center justify-center"
                            onPress={handleAttachment}
                            disabled={isAttachmentDisabled}
                        >
                            <Attachment color="#666666" width={4.5} height={4.5} />
                        </Pressable>
                        {!recordedAudioFile && (
                            <Pressable
                                className={`w-11 h-11 rounded-full items-center justify-center ${isRecording ? 'bg-gray-500' : ''}`}
                                onPress={handleMicrophonePress}
                                disabled={isMicrophoneDisabled}
                            >
                                <Microphone color={isRecording ? '#ffffff' : '#666666'} width={3} height={3} />
                            </Pressable>
                        )}
                    </View>
                </Animated.View>
            )
            }
        </>
    )
}

export default ChatInput