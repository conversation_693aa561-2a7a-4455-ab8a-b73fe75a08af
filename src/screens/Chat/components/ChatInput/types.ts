import { Animated, TextInput } from 'react-native'
import type { MessageI, MediaPreviewItem } from '../../types'

export interface ChatInputProps {
  isSelectionMode: boolean
  inputTranslateY: Animated.Value
  replyPreview: MessageI | null
  handleCloseReply: () => void
  mediaPreview: MediaPreviewItem[]
  renderMediaPreview: () => React.ReactNode
  isRecording: boolean
  recordingTime: string
  handleMicrophonePress: () => void
  recordedAudioFile: any
  renderAudioPreview: () => React.ReactNode
  textInputRef: React.RefObject<TextInput | null>
  messageText: string
  handleTextInputChange: (text: string) => void
  isTextInputDisabled: boolean
  handleSendMessage: () => void
  isSendButtonDisabled: boolean
  isAudioUploading: boolean
  handleAttachment: () => void
  isAttachmentDisabled: boolean
  isMicrophoneDisabled: boolean
}
