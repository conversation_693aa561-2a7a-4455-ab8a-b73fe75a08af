import { Dispatch, SetStateAction } from 'react';
import { StackNavigationProp } from '@react-navigation/stack';
import { UseFormReturn } from 'react-hook-form';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdNameI, IdTitleI, IdTypeI } from '@/src/types/common/data';
import { ProfileStackParamsListI } from '@/src/navigation/types';

export interface CertificationFormDataI {
  type: string;
  course: string;
  provider: string;
  providerWebsite?: string;
  validFrom: string;
  validUntil: string;
  documentUrl?: string;
  skills: string[];
}

export interface EducationFormDataI {
  institution: SearchResultI;
  degree: SearchResultI;
  fromDate: string;
  toDate: string | null;
  skills: IdTypeI[];
}

export interface UseEditEducationItemI {
  methods: UseFormReturn<EducationFormDataI>;
  isSubmitting: boolean;
  onSubmit: (data: EducationFormDataI) => Promise<void>;
  navigation: StackNavigationProp<ProfileStackParamsListI>;
  localSkills: IdNameI[];
  setLocalSkills: Dispatch<SetStateAction<SearchResultI[]>>;
  loading: boolean;
  isPresent: boolean;
  handlePresentCheckbox: () => void;
  clearFields: () => void;
  isSubmitted: boolean;
  setIsSubmitted: Dispatch<SetStateAction<boolean>>;
  hasChanges: boolean;
}

export interface EditEducationItemPropsI {
  onBack: () => void;
  profileId: string;
  educationId?: string;
}
