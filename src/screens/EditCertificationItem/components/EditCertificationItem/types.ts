import { Dispatch, SetStateAction } from 'react';
import { StackNavigationProp } from '@react-navigation/stack';
import { UseFormReturn } from 'react-hook-form';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { IdNameI, IdTitleI } from '@/src/types/common/data';
import { ProfileStackParamsListI } from '@/src/navigation/types';

export interface CertificationFormDataI {
  type: string;
  course: SearchResultI;
  provider: SearchResultI;
  validFrom: string;
  validUntil: string | null;
  documentUrl?: string;
  skills: IdNameI[];
}

export interface UseEditCertificationItemI {
  methods: UseFormReturn<CertificationFormDataI>;
  typeOptions: IdTitleI[];
  isSubmitting: boolean;
  onSubmit: (data: CertificationFormDataI) => Promise<void>;
  navigation: StackNavigationProp<ProfileStackParamsListI>;
  selectedFile: string | null;
  handleAttachment: () => void;
  localSkills: IdNameI[];
  setLocalSkills: Dispatch<SetStateAction<SearchResultI[]>>;
  loading: boolean;
  handleDownload: () => void;
  handleRemoveFile: () => void;
  isFileRemoved: boolean;
  clearFields: () => void;
  isPresent: boolean;
  handlePresentCheckbox: () => void;
  isSubmitted: boolean;
  setIsSubmitted: Dispatch<SetStateAction<boolean>>;
  hasChanges: boolean;
}

export interface EditCertificationItemPropsI {
  onBack: () => void;
  profileId: string;
  certificationId?: string;
}

export interface CompressedFileI {
  uri: string;
  type: string;
  filename: string;
}

type PreSignedUrlI = {
  accessUrl: string;
  extension: string;
  uploadUrl: string;
};

export type PreSignedUrlIResponse = PreSignedUrlI[];
