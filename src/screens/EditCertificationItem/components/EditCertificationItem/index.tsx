import { useEffect } from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Checkbox from '@/src/components/Checkbox';
import ChipInput from '@/src/components/ChipInput';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import Select from '@/src/components/Select';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { validateDate } from '@/src/screens/EditEducationItem/components/utils';
import TrashBin from '@/src/assets/svgs/TrashBin';
import Upload from '@/src/assets/svgs/Upload';
import { EditCertificationItemPropsI } from './types';
import { useEditCertificationItem } from './useHook';

export const EditCertificationItem = ({
  onBack,
  profileId,
  certificationId,
}: EditCertificationItemPropsI) => {
  const certificateCourseSelection = useSelector(selectSelectionByKey('certificate-course'));
  const providerSelection = useSelector(selectSelectionByKey('entity'));
  const {
    methods,
    typeOptions,
    isSubmitting,
    onSubmit,
    selectedFile,
    handleAttachment,
    localSkills,
    setLocalSkills,
    loading,
    handleDownload,
    handleRemoveFile,
    isFileRemoved,
    clearFields,
    isPresent,
    handlePresentCheckbox,
    isSubmitted,
    setIsSubmitted,
    hasChanges,
  } = useEditCertificationItem(profileId, certificationId);

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isDirty },
  } = methods;

  useEffect(() => {
    if (certificateCourseSelection) {
      methods.setValue('course', certificateCourseSelection);
    }
    if (providerSelection) {
      methods.setValue('provider', providerSelection);
    }
    return () => {
      clearFields();
    };
  }, [certificateCourseSelection, providerSelection, methods]);

  const validFromDate = watch('validFrom');
  const validUntilDate = watch('validUntil');
  const selectedType = watch('type');
  const selectedId =
    typeOptions.find((item) => item.title === selectedType || item.id === selectedType)?.id || '';
  const selectionKey = `certificate-course/${selectedId}`;

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-white" showsVerticalScrollIndicator={false}>
      <View className="px-4">
        <View className="flex-row items-center justify-between py-4">
          <BackButton onBack={onBack} label="Edit certification" />
          <Pressable
            onPress={() => {
              setIsSubmitted(true);
              handleSubmit(onSubmit)();
            }}
            disabled={isSubmitting || !hasChanges}
          >
            <Text
              className={`text-lg font-medium ${
                isSubmitting || !hasChanges ? 'text-gray-400' : 'text-[#448600]'
              }`}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>
        <View className="mb-3">
          <Text className="mb-2 text-base font-medium">Certificate type</Text>
          <Controller
            control={control}
            name="type"
            rules={{ required: 'Certificate type is required' }}
            disabled={!!certificationId}
            render={({ field: { onChange, value } }) => (
              <Select
                options={typeOptions}
                value={value}
                onChange={onChange}
                placeholder="Select certificate type"
                error={errors.type?.message}
                disabled={!!certificationId}
              />
            )}
          />
        </View>
        <Controller
          control={control}
          name="course"
          rules={{ required: 'Course is required' }}
          render={({ fieldState: { error } }) => (
            <EntitySearch
              placeholder="Search course"
              selectionKey={selectionKey}
              title="Certificate Course"
              data={
                certificateCourseSelection
                  ? certificateCourseSelection.name
                  : methods.watch('course')?.name || ''
              }
              error={
                isSubmitted && !certificateCourseSelection && !methods.watch('course')?.id
                  ? 'Course is required'
                  : error?.message
              }
            />
          )}
        />
        <Controller
          control={control}
          name="provider"
          rules={{ required: 'Provider is required' }}
          render={({ fieldState: { error } }) => (
            <EntitySearch
              placeholder="Search provider"
              selectionKey="entity"
              title="Provider"
              data={
                providerSelection ? providerSelection.name : methods.watch('provider')?.name || ''
              }
              error={
                isSubmitted && !providerSelection && !methods.watch('provider')?.id
                  ? 'Provider is required'
                  : error?.message
              }
            />
          )}
        />
        <View className="flex-row mb-6">
          <View className="flex-1 mr-2">
            <Controller
              control={control}
              name="validFrom"
              rules={{
                required: isPresent ? false : 'Start date is required',
                validate: (value) => validateDate(value, validUntilDate),
              }}
              render={({ field: { onChange }, fieldState: { error } }) => (
                <>
                  <DatePicker
                    title="Valid from"
                    selectedDate={validFromDate}
                    onDateChange={(date) => {
                      if (date instanceof Date) {
                        onChange(date.toISOString().split('T')[0]);
                      }
                    }}
                    showMonthYear={true}
                    className={error ? 'border-red-500' : ''}
                  />
                  {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
                </>
              )}
            />
          </View>
          <View className="flex-1 ml-2">
            <Controller
              control={control}
              name="validUntil"
              rules={{
                required: isPresent ? false : 'End date is required',
                validate: (value) => validateDate(validFromDate, value),
              }}
              render={({ field: { onChange }, fieldState: { error } }) => (
                <>
                  <DatePicker
                    title="Valid until"
                    selectedDate={isPresent ? null : validUntilDate}
                    onDateChange={(date) => {
                      if (date instanceof Date && !isPresent) {
                        onChange(date.toISOString().split('T')[0]);
                      }
                    }}
                    showMonthYear={true}
                    disabled={isPresent}
                    className={error ? 'border-red-500' : ''}
                  />
                  {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
                </>
              )}
            />
            <Checkbox
              label="Unlimited"
              className="pt-3"
              labelClassName="text-base text-sm"
              onValueChange={handlePresentCheckbox}
              checked={isPresent}
            />
          </View>
        </View>
        <View className="mb-6">
          <Text className="mb-2 text-base font-medium">Certificate document (optional)</Text>
          {selectedFile || (methods.watch('documentUrl') && !isFileRemoved) ? (
            <View className="border border-dashed border-[#E5E5E5] rounded-lg p-6">
              <View className="flex-row items-center justify-between">
                <View className="flex-1">
                  {selectedFile ? (
                    <Text className="text-sm font-medium">{selectedFile}</Text>
                  ) : (
                    <Pressable onPress={handleDownload}>
                      <Text className="text-sm font-medium text-[#448600]">
                        Certificate Document
                      </Text>
                    </Pressable>
                  )}
                </View>
                <View className="flex-row ml-3">
                  <Pressable onPress={handleAttachment} className="mr-3 p-2">
                    <Upload width={2} height={2} />
                  </Pressable>
                  <Pressable onPress={handleRemoveFile} className="p-2">
                    <TrashBin width={2} height={2} color="#DC2626" />
                  </Pressable>
                </View>
              </View>
            </View>
          ) : isFileRemoved ? (
            <View className="border border-dashed border-[#E5E5E5] rounded-xl p-6 bg-red-50/30">
              <View className="flex-row items-center justify-between">
                <View className="flex-1 pr-4">
                  <Text className="text-sm font-medium text-red-600 mb-1">
                    File marked for removal
                  </Text>
                  <Text className="text-xs text-red-500">
                    This file will be removed when you save
                  </Text>
                </View>
                <Pressable
                  onPress={handleAttachment}
                  className="p-3 bg-white rounded-lg shadow-sm active:bg-gray-50"
                >
                  <Upload width={2} height={2} color="#6B7280" />
                </Pressable>
              </View>
            </View>
          ) : (
            <Pressable
              className="border border-dashed border-[#E5E5E5] rounded-lg p-6 items-center"
              onPress={handleAttachment}
            >
              <Upload />
              <Text className="mt-2 font-medium">Upload file</Text>
              <Text style={{ fontSize: 12, color: '#6B7280' }}>
                Supported file types:
                <Text style={{ fontWeight: '500', color: '#4B5563' }}> .pdf </Text>
                (Max 4 MB),
                <Text style={{ fontWeight: '500', color: '#4B5563' }}> .jpg </Text>
                and
                <Text style={{ fontWeight: '500', color: '#4B5563' }}> .jpeg </Text>
                (Max 500 Kb).
              </Text>
            </Pressable>
          )}
        </View>
        <View className="mb-6">
          <ChipInput
            title="Skills"
            placeholder="Add a skill"
            chips={localSkills}
            onRemove={(id) => setLocalSkills((prev) => prev.filter((s) => s.id !== id))}
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default EditCertificationItem;
