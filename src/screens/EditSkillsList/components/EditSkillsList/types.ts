import { Dispatch, SetStateAction } from 'react';
import { StackNavigationProp } from '@react-navigation/stack';
import { SearchResultI } from '@/src/redux/slices/entitysearch/types';

export interface useEditSkillsListI {
  maritimeSkills: SkillI[];
  otherSkills: SkillI[];
  isSubmitting: boolean;
  handleSubmit: () => void;
  setMaritimeSkills: Dispatch<SetStateAction<SkillI[]>>;
  setOtherSkills: Dispatch<SetStateAction<SkillI[]>>;
  loading: boolean;
  loadingMore: boolean;
  loadMoreMaritime: () => void;
  loadMoreOther: () => void;
  hasMoreMaritime: boolean;
  hasMoreOther: boolean;
  activeTab: string;
  setActiveTab: Dispatch<SetStateAction<string>>;
}

export interface EditSkillsListPropsI {
  onBack: () => void;
  profileId: string;
  category: string;
}

export interface SkillI {
  id: string;
  name: string;
  dataType: string;
  category: string;
}
