import { ActivityIndicator, Pressable, ScrollView, Text, View, FlatList } from 'react-native';
import BackButton from '@/src/components/BackButton';
import Chip from '@/src/components/Chip';
import EntitySearch from '@/src/components/EntitySearch';
import Tabs from '@/src/components/Tabs';
import { EditSkillsListPropsI, SkillI } from './types';
import { useEditSkillsList } from './useHook';

const EditSkillsList = ({ onBack, profileId, category }: EditSkillsListPropsI) => {
  const {
    maritimeSkills,
    otherSkills,
    isSubmitting,
    handleSubmit,
    setMaritimeSkills,
    setOtherSkills,
    loading,
    loadingMore,
    loadMoreMaritime,
    loadMoreOther,
    hasMoreMaritime,
    hasMoreOther,
    activeTab,
    setActiveTab,
  } = useEditSkillsList(profileId, category);

  const tabs = [
    { id: 'maritimeSkills', label: 'Maritime Skills' },
    { id: 'otherSkills', label: 'Other Skills' },
  ];

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  const currentSkills = activeTab === 'maritimeSkills' ? maritimeSkills : otherSkills;
  const hasMore = activeTab === 'maritimeSkills' ? hasMoreMaritime : hasMoreOther;
  const loadMore = activeTab === 'maritimeSkills' ? loadMoreMaritime : loadMoreOther;

  return (
    <View className="flex-1 pb-10">
      <View className="flex-row items-center justify-between px-4 py-3">
        <BackButton onBack={onBack} label="" />
        <Pressable onPress={handleSubmit} disabled={isSubmitting}>
          <Text className="text-lg font-medium text-[#448600]">
            {isSubmitting ? 'Saving...' : 'Save'}
          </Text>
        </Pressable>
      </View>

      <View className="px-4">
        <Tabs tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />
      </View>

      <View className="flex-1 px-4">
        <View className="pb-4">
          <EntitySearch
            title=""
            placeholder="Add a skill"
            selectionKey="skill"
            multipleSelection={true}
            className="mt-1"
          />
        </View>

        <ScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          onScrollEndDrag={() => {
            if (hasMore && !loadingMore) {
              loadMore();
            }
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              paddingBottom: 20,
            }}
          >
            {currentSkills.map((item) => (
              <View key={item.id} style={{ marginRight: 8, marginBottom: 8 }}>
                <Chip
                  label={item.name}
                  removable={true}
                  onRemove={() => {
                    if (activeTab === 'maritimeSkills') {
                      setMaritimeSkills((prev) => prev.filter((s) => s.id !== item.id));
                    } else {
                      setOtherSkills((prev) => prev.filter((s) => s.id !== item.id));
                    }
                  }}
                  labelClassName="text-sm"
                />
              </View>
            ))}
          </View>

          {loadingMore && (
            <View style={{ padding: 20, alignItems: 'center' }}>
              <ActivityIndicator size="small" color="#448600" />
            </View>
          )}
        </ScrollView>
      </View>
    </View>
  );
};

export default EditSkillsList;
