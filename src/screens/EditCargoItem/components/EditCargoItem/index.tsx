import { ActivityIndicator, Pressable, Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import BackButton from '@/src/components/BackButton';
import DatePicker from '@/src/components/DatePicker';
import SafeArea from '@/src/components/SafeArea';
import TextInput from '@/src/components/TextInput';
import { validateFromDate, validateToDate } from '../utils';
import { CargoDetailsFormDataI, EditCargoItemPropsI } from './types';
import { useEditCargoItem } from './useHook';

const EditCargoItem = ({
  onBack,
  cargoId,
  preFilledData,
  refetch,
  shipData,
}: EditCargoItemPropsI) => {
  const { methods, onSubmit, isSubmitting, loading, hasChanges } = useEditCargoItem(
    cargoId,
    preFilledData,
    refetch,
  );
  const {
    control,
    handleSubmit,
    watch,
    formState: { errors },
  } = methods;

  const FromDate = watch('fromDate');
  const ToDate = watch('toDate');

  const renderField = (
    name: keyof Omit<CargoDetailsFormDataI, 'fromDate' | 'toDate'>,
    label: string,
  ) => (
    <Controller
      control={control}
      name={name}
      rules={{ required: `${label} is required` }}
      render={({ field: { onChange, value }, fieldState: { error } }) => (
        <TextInput
          label={label}
          value={value}
          onChangeText={onChange}
          placeholder={`Enter ${label.toLowerCase()}`}
          error={error?.message}
          className="py-3"
          key={label}
        />
      )}
    />
  );

  if (loading) {
    return (
      <SafeArea>
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="small" color="#448600" />
          <Text className="mt-4 text-gray-600">Loading cargo details...</Text>
        </View>
      </SafeArea>
    );
  }

  return (
    <View className="px-4">
      <View className="flex-row items-center justify-between py-4">
        <BackButton onBack={onBack} label="Edit Cargo" />
        <Pressable
          onPress={() => {
            handleSubmit(onSubmit)();
          }}
          disabled={isSubmitting || !hasChanges}
        >
          <Text
            className={`text-lg font-medium ${
              isSubmitting || !hasChanges ? 'text-gray-400' : 'text-[#448600]'
            }`}
          >
            {isSubmitting ? 'Saving...' : 'Save'}
          </Text>
        </Pressable>
      </View>
      {renderField('name', 'Cargo Name')}
      {renderField('description', 'Description')}
      <View className="flex-row mb-6">
        <View className="flex-1 mr-2">
          <Controller
            control={control}
            name="fromDate"
            rules={{
              required: 'Start date is required',
              validate: (value) =>
                validateFromDate(value, ToDate, shipData.fromDate, shipData.toDate),
            }}
            render={({ field: { onChange }, fieldState: { error } }) => (
              <>
                <DatePicker
                  title="From"
                  selectedDate={FromDate}
                  onDateChange={(date) => {
                    if (date instanceof Date) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  showMonthYear={true}
                  className={error ? 'border-red-500' : ''}
                />
                {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
              </>
            )}
          />
        </View>
        <View className="flex-1 ml-2">
          <Controller
            control={control}
            name="toDate"
            rules={{
              required: 'End date is required',
              validate: (value) =>
                validateToDate(value, false, FromDate, shipData.fromDate, shipData.toDate),
            }}
            render={({ field: { onChange }, fieldState: { error } }) => (
              <>
                <DatePicker
                  title="To"
                  selectedDate={ToDate}
                  onDateChange={(date) => {
                    if (date instanceof Date) {
                      onChange(date.toISOString().split('T')[0]);
                    }
                  }}
                  showMonthYear={true}
                  className={error ? 'border-red-500' : ''}
                />
                {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
              </>
            )}
          />
        </View>
      </View>
    </View>
  );
};

export default EditCargoItem;
