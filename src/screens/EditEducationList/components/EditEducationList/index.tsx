import { ActivityIndicator, FlatList, Pressable, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import BackButton from '@/src/components/BackButton';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import TextView from '@/src/components/TextView';
import AddItem from '@/src/assets/svgs/AddItem';
import EditPencil from '@/src/assets/svgs/EditPencil';
import DeleteIcon from '@/src/assets/svgs/TrashBin';
import { formatDate } from '../utils';
import type { EducationI } from './types';
import { useEditEducationList } from './useHook';

const EditEducationList = ({
  profileId,
  editable,
}: {
  onBack: () => void;
  profileId: string;
  editable: boolean;
}) => {
  const navigation = useNavigation();
  const {
    isSubmitting,
    onAddEducation,
    onEditEducation,
    onDeleteEducation,
    loading,
    loadingMore,
    handleLoadMore,
    isVisible,
    setIsVisible,
    setDeleteEducationId,
    isDeleting,
    educations,
  } = useEditEducationList(profileId);

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  const renderEducationItem = ({ item, index }: { item: EducationI; index: number }) => (
    <View className={`${index === educations.length - 1 ? `` : `border-b border-[#D4D4D4]`} p-4`}>
      <View className="flex-row justify-between items-start">
        <View className="flex-1 pr-4">
          <TextView
            subtitle={item.entity.name}
            subtitleClassName="font-medium leading-22 mt-0 font-medium text-[#000000]"
          />
          <TextView
            subtitle={item.degree.name}
            subtitleClassName="text-sm font-normal leading-[18px] mt-1 text-[#000000]"
          />
          <TextView
            subtitle={`${formatDate(item.fromDate)} - ${item.toDate ? formatDate(item.toDate) : 'Present'}`}
            subtitleClassName="text-sm font-normal-colors-neutral-500 mt-1 text-[#737373]"
          />
        </View>
        {editable && (
          <View className="flex-row">
            <Pressable onPress={() => onEditEducation(item.id)} className="p-2">
              <EditPencil width={2.3} height={2.3} />
            </Pressable>
            <Pressable
              onPress={() => {
                setDeleteEducationId(item.id);
                setIsVisible(true);
              }}
              className="p-2"
            >
              <DeleteIcon width={2.3} height={2.3} />
            </Pressable>
          </View>
        )}
      </View>
    </View>
  );

  const renderFooter = () =>
    loadingMore ? (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" color="#448600" />
      </View>
    ) : null;

  return (
    <View className="flex-1 px-4">
      <View className="flex-row items-center justify-between">
        <BackButton
          onBack={navigation.goBack}
          label={editable ? 'Edit Educations' : 'View Educations'}
        />
        {editable && (
          <Pressable onPress={onAddEducation}>
            <AddItem />
          </Pressable>
        )}
      </View>
      {educations.length === 0 ? (
        <NotFound />
      ) : (
        <>
          <FlatList
            data={educations}
            contentContainerStyle={{
              flexGrow: 1,
              backgroundColor: 'white',
              paddingVertical: 20,
            }}
            renderItem={({ item, index }) => renderEducationItem({ item, index })}
            keyExtractor={(item) => item.id}
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.3}
            ListFooterComponent={renderFooter}
            className="mt-5"
            showsHorizontalScrollIndicator={false}
          />
          <CustomModal
            isVisible={isVisible}
            onCancel={() => setIsVisible(false)}
            title="Are you sure you want to delete this education?"
            confirmText="Delete"
            confirmButtonVariant="danger"
            onConfirm={onDeleteEducation}
            isConfirming={isDeleting}
          />
        </>
      )}
    </View>
  );
};

export default EditEducationList;
