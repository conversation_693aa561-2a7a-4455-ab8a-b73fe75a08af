import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import ChipInput from '@/src/components/ChipInput';
import NotFound from '@/src/components/NotFound';
import { BottomTabNavigationI } from '@/src/navigation/types';
import { MaritimeSkillsPropsI } from './types';
import { useMaritimeSkills } from './useHook';

const MaritimeSkills: React.FC<MaritimeSkillsPropsI> = ({ isUserProfile, profileId }) => {
  const { localSkills, count } = useMaritimeSkills();
  const navigation = useNavigation<BottomTabNavigationI>();
  return (
    <View className="px-2 pt-2 pb-8">
      <ChipInput
        title="Skills"
        placeholder="Add a skill"
        chips={localSkills}
        disabled={true}
        removable={false}
        showBorder={false}
        showTitle={false}
      />
      {count > 0 && (
        <TouchableOpacity
          onPress={() =>
            navigation.navigate(isUserProfile ? 'ProfileStack' : 'HomeStack', {
              screen: 'EditSkillsList',
              params: { category: 'maritimeSkills', profileId: profileId },
            })
          }
        >
          <Text className="text-[#448600] text-base font-medium">{`View all Maritime Skills`}</Text>
        </TouchableOpacity>
      )}
      {count === 0 && (
        <NotFound
          className="pt-10 pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default MaritimeSkills;
