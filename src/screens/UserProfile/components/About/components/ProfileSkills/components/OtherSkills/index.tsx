import { ActivityIndicator, TouchableOpacity, View } from 'react-native';
import { Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import ChipInput from '@/src/components/ChipInput';
import NotFound from '@/src/components/NotFound';
import { BottomTabNavigationI } from '@/src/navigation/types';
import { OtherSkillsPropsI } from './types';
import { useOtherSkills } from './useHook';

const OtherSkills: React.FC<OtherSkillsPropsI> = ({ isUserProfile, profileId }) => {
  const { localSkills, loading } = useOtherSkills();
  const navigation = useNavigation<BottomTabNavigationI>();

  if (loading) {
    return <ActivityIndicator className="pt-14 pb-6" />;
  }

  return (
    <View className="px-2 pt-2 pb-8">
      <ChipInput
        title="Skills"
        placeholder="Add a skill"
        chips={localSkills}
        disabled={true}
        removable={false}
        showBorder={false}
        showTitle={false}
      />
      {localSkills.length > 0 && (
        <TouchableOpacity
          onPress={() =>
            navigation.navigate(isUserProfile ? 'ProfileStack' : 'HomeStack', {
              screen: 'EditSkillsList',
              params: { category: 'otherSkills', profileId: profileId },
            })
          }
        >
          <Text className="text-[#448600] text-base font-medium">{`View all Other Skills`}</Text>
        </TouchableOpacity>
      )}
      {localSkills.length === 0 && (
        <NotFound
          className="pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default OtherSkills;
