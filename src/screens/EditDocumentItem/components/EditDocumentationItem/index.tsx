import { useEffect } from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Checkbox from '@/src/components/Checkbox';
import DatePicker from '@/src/components/DatePicker';
import EntitySearch from '@/src/components/EntitySearch';
import PdfDownload from '@/src/components/PdfDownload';
import Select from '@/src/components/Select';
import TextInput from '@/src/components/TextInput';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { validateDate } from '@/src/screens/EditEducationItem/components/utils';
import TrashBin from '@/src/assets/svgs/TrashBin';
import Upload from '@/src/assets/svgs/Upload';
import { EditDocumentItemPropsI } from './types';
import { useEditDocumentItem } from './useHook';

export const EditDocumentItem = ({
  onBack,
  profileId,
  documentId,
  type,
}: EditDocumentItemPropsI) => {
  const countrySelection = useSelector(selectSelectionByKey('country'));
  const {
    methods,
    documentTypeOptions,
    documentNameOptions,
    isSubmitting,
    onSubmit,
    handleAttachment,
    selectedFile,
    loading,
    handleDownload,
    handleRemoveFile,
    isFileRemoved,
    clearFields,
    isPresent,
    handlePresentCheckbox,
    isSubmitted,
    setIsSubmitted,
  } = useEditDocumentItem(profileId, documentId, type);

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isDirty },
  } = methods;

  useEffect(() => {
    if (countrySelection) {
      methods.setValue('country', countrySelection);
    }
    return () => {
      clearFields();
    };
  }, [countrySelection, methods]);

  const FromDate = watch('validFrom');
  const toDate = watch('validUntil');

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-white" showsVerticalScrollIndicator={false}>
      <View className="px-4">
        <View className="flex-row items-center justify-between py-4">
          <BackButton onBack={onBack} label="" />
          <Pressable
            onPress={() => {
              setIsSubmitted(true);
              handleSubmit(onSubmit)();
            }}
            disabled={isSubmitting || !isDirty}
          >
            <Text
              className={`text-lg font-medium ${
                isSubmitting || !isDirty ? 'text-gray-400' : 'text-[#448600]'
              }`}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        </View>
        <View className="mb-6">
          <Text className="mb-2 text-base font-medium">Document type</Text>
          <Controller
            control={control}
            name="documentType"
            rules={{ required: 'Document type is required' }}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <Select
                options={documentTypeOptions}
                value={value}
                onChange={onChange}
                placeholder="Select document type"
                error={error?.message}
                disabled={!!documentId}
              />
            )}
          />
        </View>
        <View className="mb-6">
          {methods.watch('documentType')?.toLowerCase() !== 'identity' ? (
            <Controller
              control={control}
              name="documentName"
              rules={{
                required: 'Visa type is required',
              }}
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <TextInput
                  label="Visa Type"
                  placeholder="Enter type"
                  value={value}
                  onChangeText={onChange}
                  editable={documentId ? false : true}
                  error={error?.message}
                />
              )}
            />
          ) : (
            <>
              <Text className="mb-2 text-base font-medium">Document name</Text>
              <Controller
                control={control}
                name="documentName"
                rules={{ required: 'Document name is required' }}
                render={({ field: { onChange, value }, fieldState: { error } }) => (
                  <Select
                    options={documentNameOptions}
                    value={value}
                    onChange={onChange}
                    placeholder="Select document name"
                    error={error?.message}
                    disabled={!!documentId}
                  />
                )}
              />
            </>
          )}
        </View>
        <View className="mb-6">
          <Text className="mb-3 text-base font-semibold text-gray-900">
            Upload document (optional)
          </Text>
          {selectedFile || (methods.watch('documentFile') && !isFileRemoved) ? (
            <View className="border border-dashed border-[#E5E5E5] rounded-xl p-6 bg-gray-50/30">
              <View className="flex-row items-center justify-between">
                <View className="flex-1 pr-4">
                  {selectedFile ? (
                    <View>
                      <Text className="text-sm font-semibold text-gray-900 mb-1">
                        {selectedFile}
                      </Text>
                      <Text className="text-xs text-gray-500">File ready for upload</Text>
                    </View>
                  ) : (
                    <View className="flex-row items-center">
                      <PdfDownload onDownload={handleDownload} />
                    </View>
                  )}
                </View>
                <View className="flex-row items-center space-x-2">
                  <Pressable
                    onPress={handleAttachment}
                    className="p-3 bg-white rounded-lg shadow-sm active:bg-gray-50"
                  >
                    <Upload width={2} height={2} color="#6B7280" />
                  </Pressable>
                  <Pressable
                    onPress={handleRemoveFile}
                    className="p-3 bg-white rounded-lg shadow-sm active:bg-red-50"
                  >
                    <TrashBin width={2} height={2} color="#DC2626" />
                  </Pressable>
                </View>
              </View>
            </View>
          ) : isFileRemoved ? (
            <View className="border border-dashed border-[#E5E5E5] rounded-xl p-6 bg-red-50/30">
              <View className="flex-row items-center justify-between">
                <View className="flex-1 pr-4">
                  <Text className="text-sm font-medium text-red-600 mb-1">
                    File marked for removal
                  </Text>
                  <Text className="text-xs text-red-500">
                    This file will be removed when you save
                  </Text>
                </View>
                <Pressable
                  onPress={handleAttachment}
                  className="p-3 bg-white rounded-lg shadow-sm active:bg-gray-50"
                >
                  <Upload width={2} height={2} color="#6B7280" />
                </Pressable>
              </View>
            </View>
          ) : (
            <Pressable
              className="border border-dashed border-[#E5E5E5] rounded-xl p-8 items-center bg-gray-50/20 active:bg-gray-50/40"
              onPress={handleAttachment}
            >
              <View className="w-12 h-12 bg-gray-100 rounded-full items-center justify-center mb-4">
                <Upload width={2} height={2} color="#6B7280" />
              </View>
              <Text className="text-base font-semibold text-gray-900 mb-2">Upload file</Text>
              <View className="items-center">
                <Text className="text-sm text-[#6B7280] text-center leading-5">
                  Supported file types:
                  <Text className="font-semibold text-[#4B5563]"> .pdf </Text>
                  (Max 4 MB),
                  <Text className="font-semibold text-[#4B5563]"> .jpg </Text>
                  and
                  <Text className="font-semibold text-[#4B5563]"> .jpeg </Text>
                  (Max 500 Kb).
                </Text>
              </View>
            </Pressable>
          )}
        </View>
        <View className="mb-3">
          <Controller
            control={control}
            name="documentNumber"
            rules={{
              required: 'Document number is required',
              minLength: {
                value: 3,
                message: 'Document number must be at least 3 characters',
              },
            }}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <TextInput
                label="Document number"
                placeholder="Enter document number"
                value={value}
                onChangeText={onChange}
                error={error?.message}
              />
            )}
          />
        </View>
        <Controller
          control={control}
          name="country"
          rules={{ required: 'Country is required' }}
          render={({ fieldState: { error } }) => (
            <EntitySearch
              placeholder="Search country"
              selectionKey="country"
              title="Country"
              data={countrySelection ? countrySelection.name : methods.watch('country')?.name || ''}
              error={
                isSubmitted && !countrySelection && !methods.watch('country')?.id
                  ? `Country is required`
                  : error?.message
              }
            />
          )}
        />
        <View className="flex-row mb-6 mt-3">
          <View className="flex-1 mr-2">
            <Controller
              control={control}
              name="validFrom"
              rules={{
                required: 'Valid From date is required',
                validate: (value) => validateDate(value, toDate),
              }}
              render={({ field: { onChange }, fieldState: { error } }) => (
                <>
                  <DatePicker
                    title="Valid from"
                    selectedDate={FromDate}
                    onDateChange={(date) => {
                      if (date instanceof Date) {
                        onChange(date.toISOString().split('T')[0]);
                      }
                    }}
                    showMonthYear={true}
                    className={error ? 'border-red-500' : ''}
                  />
                  {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
                </>
              )}
            />
          </View>
          <View className="flex-1 ml-2">
            <Controller
              control={control}
              name="validUntil"
              rules={{
                required: isPresent ? false : 'Valid Until date is required',
                validate: (value) => validateDate(FromDate, value),
              }}
              render={({ field: { onChange }, fieldState: { error } }) => (
                <>
                  <DatePicker
                    title="Valid until"
                    selectedDate={toDate!}
                    onDateChange={(date) => {
                      if (date instanceof Date) {
                        onChange(date.toISOString().split('T')[0]);
                      }
                    }}
                    showMonthYear={true}
                    disabled={isPresent}
                    className={error ? 'border-red-500' : ''}
                  />
                  {error && <Text className="text-red-500 text-xs mt-1">{error.message}</Text>}
                </>
              )}
            />
            <Checkbox
              label="Unlimited"
              className="pt-3"
              labelClassName="text-base text-sm"
              onValueChange={handlePresentCheckbox}
              checked={isPresent}
            />
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

export default EditDocumentItem;
