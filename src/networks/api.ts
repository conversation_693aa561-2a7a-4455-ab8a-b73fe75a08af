/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Platform } from 'react-native';
import Config from 'react-native-config';
import { resetUserState } from '@/src/redux/slices/user/userSlice';
import { store } from '@/src/redux/store';
import { isFilled, omit } from '@/src/utilities/data/object';
import { generateUrl } from '@/src/utilities/networks/api';
import { clearAllStorage, getStorage } from '@/src/utilities/storage/storage';
import APIResError from '@/src/errors/networks/APIResError';
import AppError from '@/src/errors/networks/AppError';
import { APICallI, HeadersI, MethodI } from '@/src/networks/types';
import { version } from '../../package.json';

// const BASE_URL = Config.BASE_URL;
const BASE_URL = 'https://api.b2c.navicater.com';
console.log(BASE_URL, 'baseURL Check')
const getHeaders = async (): Promise<HeadersI> => ({
  'x-api-key': Config.API_KEY || '',
  'Content-Type': 'application/json',
  Accept: 'application/json',
  'x-device-id': (await getStorage('deviceId')) || '',
  'x-platform': Platform.OS,
  'x-version-no': version,
});

export const apiCall = async <PayloadT = unknown, ResponseT = unknown>(
  path: string,
  method: MethodI,
  { isAuth = true, payload, query, routeId, headers }: APICallI<PayloadT>,
): Promise<ResponseT> => {
  try {
    console.log(BASE_URL, 'baseURl')
    const url = generateUrl({
      baseUrl: BASE_URL,
      path,
      query,
      routeId,
    });

    let baseHeaders = await getHeaders();

    if (isFilled(headers)) {
      baseHeaders = { ...baseHeaders, ...headers };
    }

    if (method === 'DELETE') {
      baseHeaders = omit(baseHeaders, ['Content-Type', 'Accept']) as HeadersI;
    }

    if (isAuth) {
      const token = await getStorage('token');
      if (token) {
        baseHeaders.Authorization = `Bearer ${token}`;
      }
    }

    const options: RequestInit = {
      headers: baseHeaders,
      method,
    };

    if (isFilled(payload)) {
      options.body = JSON.stringify(payload);
    }

    const response = await fetch(url, options);

    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');
    const json = isJson ? await response.json() : null;
    // console.log(json, query, payload, 'jsonChecking....')
    if (response.status >= 200 && response.status < 300) {
      return json as ResponseT;
    } else if (response.status === 401) {
      await clearAllStorage();
      store.dispatch(resetUserState());
    }

    throw new APIResError(response.status, json || { message: 'No response body' });
  } catch (error) {
    if (error instanceof APIResError) {
      throw error;
    } else if (error instanceof TypeError) {
      throw error;
    }
    throw new AppError('Unknown error', error as Error);
  }
};
